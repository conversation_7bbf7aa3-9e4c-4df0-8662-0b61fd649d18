from pydantic import BaseModel, field_validator
from uuid import UUID
from typing import Union
from Models.users import UserTypeEnum

class UserBase(BaseModel):
    username: str
    email: str
    mobile: str
    user_type: Union[str, UserTypeEnum]  # Can be either string or enum
    country: str | None = None  # Optional field for country
    profile_picture: str | None = None  # Optional field for profile picture URL

    @field_validator('user_type', mode='before')
    @classmethod
    def validate_user_type(cls, v):
        if isinstance(v, UserTypeEnum):
            return v.value
        return v

    class Config:
        from_attributes = True
        orm_mode = True

class UserCreate(UserBase):
    password: str
    cnic: str | None = None  # Optional field for CNIC
    passport: str | None = None  # Optional field for Passport

class UserOut(UserBase):
    id: UUID
    is_email_verified: bool = False
    is_mobile_verified: bool = False

    class Config:
        from_attributes = True
        orm_mode = True

class Signin(BaseModel):
    email: str
    password: str