from pydantic import BaseModel
from typing import Optional
from uuid import UUID
from datetime import datetime
from enum import Enum

class ExamCheckingStatusEnum(str, Enum):
    pending = "pending"
    checked = "checked"
    rejected = "rejected"

class ExamCheckingBase(BaseModel):
    exam_id: UUID
    student_id: UUID
    status: ExamCheckingStatusEnum = ExamCheckingStatusEnum.pending
    score: Optional[int] = None
    feedback: Optional[str] = None
    checked_at: Optional[datetime] = None
    checked_by: Optional[UUID] = None

class ExamCheckingCreate(ExamCheckingBase):
    pass

class ExamCheckingUpdate(BaseModel):
    status: Optional[ExamCheckingStatusEnum] = None
    score: Optional[int] = None
    feedback: Optional[str] = None
    checked_at: Optional[datetime] = None
    checked_by: Optional[UUID] = None

class ExamCheckingOut(ExamCheckingBase):
    id: UUID
    class Config:
        orm_mode = True 