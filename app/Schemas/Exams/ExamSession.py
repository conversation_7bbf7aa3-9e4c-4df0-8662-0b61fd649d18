from pydantic import BaseModel
from typing import Dict, Optional
from datetime import datetime

# --- Session Management Schemas ---
class StartSessionRequest(BaseModel):
    exam_id: str

class StartSessionResponse(BaseModel):
    session_id: str

class SubmitSessionRequest(BaseModel):
    session_id: str

class SubmitSessionResponse(BaseModel):
    success: bool
    disqualified: bool
    disqualification_reason: Optional[str] = None

# --- Reconnection Schemas ---
class ReconnectionRequest(BaseModel):
    session_id: str
    reason: str

class ReconnectionRequestResponse(BaseModel):
    request_id: str
    status: str

class TeacherApprovalRequest(BaseModel):
    request_id: str
    approved: bool
    reason: Optional[str] = None

class TeacherApprovalResponse(BaseModel):
    status: str
    session_id: Optional[str] = None

class ReconnectionStatusResponse(BaseModel):
    status: str
    teacher_reason: Optional[str] = None
    session_id: Optional[str] = None

# --- Session Resume Schemas ---
class SessionResumeResponse(BaseModel):
    session_id: str
    exam_id: str
    exam_title: str
    current_answers: Dict[str, str]
    remaining_time_seconds: int
    total_duration_seconds: int
    strikes: int
    status: str

# --- Admin Schemas ---
class AdminViewSessionResponse(BaseModel):
    session_id: str
    student_id: str
    exam_id: str
    answers: str
    strikes: str
    last_heartbeat: str
    status: str
    start_time: str
    duration: str

class AdminListSessionsResponse(BaseModel):
    session_id: str
    student_id: str
    exam_id: str
    answers: str
    strikes: str
    last_heartbeat: str
    status: str
    start_time: str
    duration: str

class PendingReconnectionRequest(BaseModel):
    request_id: str
    session_id: str
    student_id: str
    exam_id: str
    reason: str
    status: str
    requested_at: str

# --- WebSocket Message Schemas ---
class WebSocketMessage(BaseModel):
    type: str
    data: Optional[Dict] = None

class SessionResumeMessage(BaseModel):
    type: str = "session_resume"
    current_answers: Dict[str, str]
    remaining_time_seconds: int
    strikes: int
    session_id: str

class AnswersSavedMessage(BaseModel):
    type: str = "answers_saved"
    timestamp: str

class DisqualifiedMessage(BaseModel):
    event: str = "disqualified"
    reason: str 