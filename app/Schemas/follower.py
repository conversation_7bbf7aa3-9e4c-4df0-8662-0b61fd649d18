from pydantic import BaseModel
from uuid import UUID

class FollowerSchema(BaseModel):
    user_id: UUID 
    follower_id: UUID 

    class Config:
        orm_mode = True

class FollowRequestSchema(BaseModel):
    user_id: UUID 
    follower_id: UUID 

    class Config:
        orm_mode = True

class FollowerCreateSchema(BaseModel):
    user_id: UUID 
    follower_id: UUID 

    class Config:
        orm_mode = True
class FollowRequestCreateSchema(BaseModel):
    user_id: UUID 
    follower_id: UUID 

    class Config:
        orm_mode = True

class FollowerRemoveSchema(BaseModel):
    user_id: UUID 
    follower_id: UUID 

    class Config:
        orm_mode = True
class FollowRequestRemoveSchema(BaseModel):
    user_id: UUID 
    follower_id: UUID 

    class Config:
        orm_mode = True

class FollowerOutputSchema(BaseModel):
    user_id: UUID 
    follower_id: UUID 

    class Config:
        orm_mode = True

class FollowRequestOutputSchema(BaseModel):
    user_id: UUID 
    follower_id: UUID 

    class Config:
        orm_mode = True

