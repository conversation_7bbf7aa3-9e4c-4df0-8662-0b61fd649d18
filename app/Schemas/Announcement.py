from pydantic import BaseModel, Field
from typing import Optional
from uuid import UUID
from datetime import datetime

class AnnouncementBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200, description="Title of the announcement")
    content: str = Field(..., min_length=1, max_length=2000, description="Content of the announcement")

class AnnouncementCreate(AnnouncementBase):
    pass

class AnnouncementUpdate(AnnouncementBase):
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="Title of the announcement")
    content: Optional[str] = Field(None, min_length=1, max_length=2000, description="Content of the announcement")

class AnnouncementInDBBase(AnnouncementBase):
    id: UUID
    classroom_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class Announcement(AnnouncementInDBBase):
    pass 