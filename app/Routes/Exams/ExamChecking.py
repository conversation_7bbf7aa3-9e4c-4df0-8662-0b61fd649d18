from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from Schemas.Exams.ExamChecking import Exam<PERSON>hecking<PERSON><PERSON>, ExamCheckingUpdate, ExamCheckingOut
from Cruds.Exams.ExamChecking import (
    create_exam_checking,
    get_exam_checking_by_id,
    get_all_exam_checkings,
    update_exam_checking,
    delete_exam_checking
)
from config.session import get_db
from typing import List
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

router = APIRouter()

@router.post("/", response_model=ExamCheckingOut)
def create_checking_endpoint(
    checking_in: ExamCheckingCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    return create_exam_checking(db, checking_in)

@router.get("/{checking_id}", response_model=ExamCheckingOut)
def get_checking_by_id_endpoint(
    checking_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    return get_exam_checking_by_id(db, checking_id)

@router.get("/", response_model=List[ExamCheckingOut])
def get_all_checkings_endpoint(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    return get_all_exam_checkings(db)

@router.put("/{checking_id}", response_model=ExamCheckingOut)
def update_checking_endpoint(
    checking_id: UUID,
    checking_update: ExamCheckingUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    return update_exam_checking(db, checking_id, checking_update)

@router.delete("/{checking_id}", status_code=204)
def delete_checking_endpoint(
    checking_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    delete_exam_checking(db, checking_id)
    return None 