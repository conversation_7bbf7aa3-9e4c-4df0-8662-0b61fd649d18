from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from Schemas.Exams.Questions import QuestionCreate, QuestionUpdate, QuestionOut, McqOptionUpdate, McqOptionOut
from Cruds.Exams.Questions import (
    create_question,
    get_all_questions,
    get_question_by_id,
    update_question,
    delete_question,
    update_mcq_option_text
)
from config.session import get_db
from typing import List
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type
from pydantic import BaseModel, Field
from typing import Optional
from Cruds.Exams.Questions import call_gemini_with_ai_data

router = APIRouter()

@router.get("/", response_model=List[QuestionOut])
def read_questions(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    return get_all_questions(db)

@router.get("/{question_id}", response_model=QuestionOut)
def read_question(question_id: UUID, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    return get_question_by_id(db, question_id)

@router.post("/", response_model=QuestionOut)
def create_question_endpoint(
    question_in: QuestionCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    user = get_current_user(token, db)
    return create_question(db, question_in, user)

@router.put("/{question_id}", response_model=QuestionOut)
def update_question_endpoint(question_id: UUID, question_update: QuestionUpdate, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    return update_question(db, question_id, question_update)

@router.put("/options/{option_id}", response_model=McqOptionOut)
def update_option_text(option_id: UUID, option_update: McqOptionUpdate, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    return update_mcq_option_text(db, option_id, option_update)

@router.delete("/{question_id}", status_code=204)
def delete_question_endpoint(question_id: UUID, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    delete_question(db, question_id)
    return None

class AIQuestionGenRequest(BaseModel):
    class_: str = Field(..., alias="class")
    subject: str
    chapter: str
    no_of_questions: Optional[int] = 3
    topic: Optional[str] = None
    subtopic: Optional[str] = None

@router.post("/ai-generate")
async def ai_generate_questions(
    request: AIQuestionGenRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    result = await call_gemini_with_ai_data(
        class_=request.class_,
        subject=request.subject,
        chapter=request.chapter,
        no_of_questions=request.no_of_questions or 3,
        topic=request.topic,
        subtopic=request.subtopic
    )
    return result 