from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from Schemas.Exams.Exam import ExamCreate, ExamUpdate, ExamOut, ExamCreateWithAssignment, ExamAssignmentOut, ExamStudentOut, ExamStudentMinimalOut
from Cruds.Exams.Exam import (
    get_exam_by_id,
    get_all_exams,
    update_exam,
    delete_exam,
    create_exam_with_assignment,
    get_exams_by_teacher,
    get_upcoming_exams_for_student,
    get_exam_for_student
)
from config.session import get_db
from typing import List
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type
from Models.Questions import Question
from datetime import timedelta, datetime

router = APIRouter()

def is_teacher_exam(db: Session, exam_id: UUID, teacher_id) -> bool:
    # Ensure teacher_id is a value, not a Column
    question = db.query(Question).filter(
        Question.teacher_id == teacher_id,
        Question.exams.any(id=exam_id)
    ).first()
    return question is not None

@router.get("/", response_model=List[ExamOut])
def read_exams(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    return get_all_exams(db)

@router.get("/{exam_id}", response_model=ExamOut)
def read_exam(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    return get_exam_by_id(db, exam_id, teacher_id=teacher_id)

@router.post("/", response_model=ExamOut)
def create_exam_endpoint(
    exam_in: ExamCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    # This endpoint is not implemented in the CRUD, so raise an error or implement if needed
    raise HTTPException(status_code=501, detail="Not implemented. Use /create-with-assignment instead.")

@router.put("/{exam_id}", response_model=ExamOut)
def update_exam_endpoint(
    exam_id: UUID,
    exam_update: ExamUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    return update_exam(db, exam_id, exam_update, teacher_id=teacher_id)

@router.delete("/{exam_id}", status_code=204)
def delete_exam_endpoint(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    delete_exam(db, exam_id, teacher_id=teacher_id)
    return None

@router.post("/create-with-assignment", response_model=ExamAssignmentOut)
def create_exam_with_assignment_endpoint(
    exam_in: ExamCreateWithAssignment,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token, db)
    return create_exam_with_assignment(db, exam_in, current_user)

@router.get("/my-exams", response_model=List[ExamOut])
def get_teacher_exams(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    if teacher_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")
    return get_exams_by_teacher(db, teacher_id)

@router.get("/student/upcoming", response_model=List[ExamStudentMinimalOut])
def get_student_upcoming_exams(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    current_user = get_current_user(token, db)
    student_id = getattr(current_user, 'id', None)
    if student_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")
    exams = get_upcoming_exams_for_student(db, student_id)
    result = []
    for exam in exams:
        start_time = getattr(exam, 'start_time', None)
        total_duration = getattr(exam, 'total_duration', None)
        end_time = None
        if start_time and total_duration:
            end_time = start_time + timedelta(minutes=int(total_duration))
        result.append(ExamStudentMinimalOut(
            id=getattr(exam, 'id', None),
            title=getattr(exam, 'title', None),
            start_time=start_time,
            end_time=end_time,
            total_duration=int(total_duration) if total_duration is not None else 0
        ))
    return result

@router.get("/student/{exam_id}", response_model=ExamStudentOut)
def get_student_exam(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    current_user = get_current_user(token, db)
    student_id = getattr(current_user, 'id', None)
    if student_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")
    exam = get_exam_for_student(db, exam_id, student_id)
    now = datetime.utcnow()
    start_time = getattr(exam, 'start_time', None)
    total_duration = getattr(exam, 'total_duration', None)
    if start_time and now < start_time:
        raise HTTPException(status_code=403, detail="Exam not started yet.")
    return ExamStudentOut(
        id=getattr(exam, 'id', None),
        questions=[q for q in exam.questions],
        end_time=(start_time + timedelta(minutes=int(total_duration))) if start_time and total_duration else None
    ) 



