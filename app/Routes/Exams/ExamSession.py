from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status, BackgroundTasks, Query
from pydantic import BaseModel
from uuid import uuid4
import asyncio
from sqlalchemy.orm import Session
from config.session import get_db
from datetime import datetime, timedelta
import json
from config.redis import get_redis
from config.mongodb import get_mongodb
from Models.users import User
from Models.Exam import Exam, StudentExamAttempt, StudentExamAnswer
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type
from Schemas.Exams.ExamSession import (
    StartSessionRequest, StartSessionResponse, SubmitSessionRequest, SubmitSessionResponse,
    ReconnectionRequest, ReconnectionRequestResponse, TeacherApprovalRequest, TeacherApprovalResponse,
    ReconnectionStatusResponse, SessionResumeResponse, PendingReconnectionRequest
)
from Cruds.Exams.ExamSession import ExamSessionCRUD
from typing import List

router = APIRouter()

# --- Endpoint: Start Exam Session ---
@router.post("/exam-session/start", response_model=StartSessionResponse)
async def start_exam_session(
    req: StartSessionRequest,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    crud = ExamSessionCRUD(redis, mongo_db)
    session_id = await crud.start_session(db, str(current_user.id), req.exam_id, mongo_db)
    return StartSessionResponse(session_id=session_id)

# --- Endpoint: Final Submission ---
@router.post("/exam-session/submit", response_model=SubmitSessionResponse)
async def submit_exam_session(
    req: SubmitSessionRequest,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    crud = ExamSessionCRUD(redis, mongo_db)
    result = await crud.submit_session(db, req.session_id, str(current_user.id), mongo_db)
    return SubmitSessionResponse(**result)

# --- Student Reconnection Request ---
@router.post("/exam-session/{session_id}/request-reconnection", response_model=ReconnectionRequestResponse)
async def request_reconnection(
    session_id: str,
    req: ReconnectionRequest,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    crud = ExamSessionCRUD(redis, mongo_db)
    request_id = await crud.request_reconnection(db, session_id, str(current_user.id), req.reason, mongo_db)
    return ReconnectionRequestResponse(request_id=request_id, status="pending_approval")

# --- Teacher View Pending Requests ---
@router.get("/admin/reconnection-requests", response_model=List[PendingReconnectionRequest])
async def get_pending_reconnection_requests(
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    token: str = Depends(oauth2_scheme),
    teacher: User = Depends(require_type("teacher"))
):
    crud = ExamSessionCRUD(redis)
    requests = await crud.get_pending_reconnection_requests(db, str(teacher.id))
    return [PendingReconnectionRequest(**req) for req in requests]

# --- Teacher Approve/Deny Request ---
@router.post("/admin/reconnection-request/{request_id}/approve", response_model=TeacherApprovalResponse)
async def approve_reconnection_request(
    request_id: str,
    req: TeacherApprovalRequest,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    teacher: User = Depends(require_type("teacher"))
):
    crud = ExamSessionCRUD(redis, mongo_db)
    result = await crud.approve_reconnection_request(db, request_id, str(teacher.id), req.approved, req.reason or "", mongo_db)
    await crud.log_admin_action(mongo_db, str(teacher.id), "approve_reconnection", request_id, req.reason or "")
    return TeacherApprovalResponse(**result)

# --- Student Check Reconnection Status ---
@router.get("/exam-session/reconnection-status/{request_id}", response_model=ReconnectionStatusResponse)
async def check_reconnection_status(
    request_id: str,
    redis=Depends(get_redis),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    request_key = f"reconnection_request:{request_id}"
    request = await redis.hgetall(request_key)
    if not request:
        raise HTTPException(status_code=404, detail="Reconnection request not found.")
    
    if request.get("student_id") != str(current_user.id):
        raise HTTPException(status_code=403, detail="You can only check your own requests.")
    
    return ReconnectionStatusResponse(
        status=request.get("status"),
        teacher_reason=request.get("teacher_reason"),
        session_id=request.get("session_id") if request.get("status") == "approved" else None
    )

# --- Student Resume Session After Approval ---
@router.get("/exam-session/{session_id}/resume", response_model=SessionResumeResponse)
async def resume_exam_session(
    session_id: str,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    crud = ExamSessionCRUD(redis)
    return await crud.get_session_resume_data(db, session_id, str(current_user.id))

# --- WebSocket Endpoint ---
@router.websocket("/ws/exam-session/{session_id}")
async def exam_session_ws(
    websocket: WebSocket,
    session_id: str,
    redis=Depends(get_redis),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    await websocket.accept()
    session_key = f"exam_session:{session_id}"
    session = await redis.hgetall(session_key)
    
    if not session:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    
    # Security: Only allow the student who owns the session to connect
    if session.get("student_id") != str(current_user.id):
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    
    # Check if session is active
    if session.get("status") != "active":
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    
    # Send current session state to student immediately upon connection
    current_answers = json.loads(session.get("answers", "{}"))
    start_time = datetime.fromisoformat(session["start_time"])
    duration_seconds = int(session["duration"])
    end_time = start_time + timedelta(seconds=duration_seconds)
    remaining_seconds = max(0, int((end_time - datetime.utcnow()).total_seconds()))
    
    # Send initial state
    await websocket.send_json({
        "type": "session_resume",
        "current_answers": current_answers,
        "remaining_time_seconds": remaining_seconds,
        "strikes": int(session.get("strikes", 0)),
        "session_id": session_id
    })
    
    strikes = int(session.get("strikes", 0))
    last_heartbeat = datetime.utcnow()
    missed_heartbeats = 0
    
    try:
        while True:
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=40)  # 2 missed heartbeats = 40s
                msg = json.loads(data)
                
                if msg.get("type") == "heartbeat":
                    last_heartbeat = datetime.utcnow()
                    await redis.hset(session_key, "last_heartbeat", last_heartbeat.isoformat())
                    missed_heartbeats = 0
                    
                elif msg.get("type") == "cheat":
                    strikes = int(await redis.hincrby(session_key, "strikes", 1))
                    if strikes >= 3:
                        await redis.hset(session_key, mapping={"status": "disqualified"})
                        await websocket.send_json({"event": "disqualified", "reason": "Cheating detected."})
                        await websocket.close()
                        return
                        
                elif "answers" in msg:
                    # Sync answers
                    await redis.hset(session_key, "answers", json.dumps(msg["answers"]))
                    # Send confirmation
                    await websocket.send_json({
                        "type": "answers_saved",
                        "timestamp": datetime.utcnow().isoformat()
                    })
                    
                # else: ignore unknown
                
            except asyncio.TimeoutError:
                missed_heartbeats += 1
                if missed_heartbeats >= 2:
                    await redis.hset(session_key, mapping={"status": "disconnected"})
                    await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
                    return
                    
    except WebSocketDisconnect:
        await redis.hset(session_key, mapping={"status": "disconnected"})
        return

# --- ADMIN ENDPOINTS ---

@router.get("/admin/exam-session/{session_id}")
async def admin_view_session(
    session_id: str,
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    admin: User = Depends(require_type("admin")),
):
    session_key = f"exam_session:{session_id}"
    session = await redis.hgetall(session_key)
    crud = ExamSessionCRUD(redis, mongo_db)
    await crud.log_admin_action(mongo_db, str(admin.id), "view", session_id, "")
    if not session:
        raise HTTPException(status_code=404, detail="Session not found.")
    return session

@router.post("/admin/exam-session/{session_id}/submit")
async def admin_force_submit(
    session_id: str,
    reason: str = Query(..., description="Reason for force submission"),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    admin: User = Depends(require_type("admin")),
):
    crud = ExamSessionCRUD(redis, mongo_db)
    await crud.log_admin_action(mongo_db, str(admin.id), "force_submit", session_id, reason)
    session_key = f"exam_session:{session_id}"
    session = await redis.hgetall(session_key)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found.")
    
    # Save answers and disqualification reason to PostgreSQL
    answers = json.loads(session.get("answers", "{}"))
    strikes = int(session.get("strikes", 0))
    status_val = session.get("status", "ended")
    disqualification_reason = reason if status_val == "disqualified" else None
    attempt = StudentExamAttempt(
        exam_id=session["exam_id"],
        student_id=session["student_id"],
        started_at=datetime.utcnow(),
        completed_at=datetime.utcnow(),
        is_teacher_checked=False,
        is_ai_checked=False,
    )
    if disqualification_reason:
        attempt.status = "disqualified"
    db.add(attempt)
    db.commit()
    db.refresh(attempt)
    for qid, ans in answers.items():
        db.add(StudentExamAnswer(
            attempt_id=attempt.id,
            question_id=qid,
            answer=ans
        ))
    db.commit()
    await redis.delete(session_key)
    return {"success": True, "admin_forced": True, "disqualification_reason": disqualification_reason}

@router.post("/admin/exam-session/{session_id}/terminate")
async def admin_terminate_session(
    session_id: str,
    reason: str = Query(..., description="Reason for termination"),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    admin: User = Depends(require_type("admin")),
):
    session_key = f"exam_session:{session_id}"
    session = await redis.hgetall(session_key)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found.")
    await redis.hset(session_key, mapping={"status": "terminated_by_admin"})
    crud = ExamSessionCRUD(redis, mongo_db)
    await crud.log_admin_action(mongo_db, str(admin.id), "terminate", session_id, reason)
    return {"success": True, "terminated": True, "reason": reason}

@router.get("/admin/exam-sessions/active")
async def admin_list_active_sessions(
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    admin: User = Depends(require_type("admin")),
):
    crud = ExamSessionCRUD(redis, mongo_db)
    sessions = await crud.get_active_sessions()
    await crud.log_admin_action(mongo_db, str(admin.id), "list_active", "*", "")
    return sessions 