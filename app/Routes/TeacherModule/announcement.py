from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from Schemas.Announcement import Announcement, AnnouncementCreate, AnnouncementUpdate
from Cruds import announcement as crud_announcement
from config.session import get_db
from Models.Announcements import Announcement as AnnouncementModel
from config.permission import require_type
from config.security import oauth2_scheme
from Models.Classroom import Classroom
from config.deps import get_current_user

router = APIRouter()

@router.post("/{classroom_id}", response_model=Announcement)
def create_announcement(
    classroom_id: UUID,
    announcement_in: AnnouncementCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Create a new announcement. Only accessible to teachers.
    """
    try:
        # Validate that the classroom exists and the teacher has access to it
        current_user = get_current_user(token, db)
        classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
        
        if not classroom:
            raise HTTPException(status_code=404, detail="Classroom not found")
        
        # Check if the current user is the teacher of this classroom
        if str(classroom.teacher_id) != str(current_user.id):
            raise HTTPException(status_code=403, detail="You can only create announcements in your own classrooms")
        
        return crud_announcement.create(db, announcement_in, classroom_id)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/", response_model=List[Announcement])
def read_announcements_by_query(
    classroom_id: UUID = Query(..., description="Classroom ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    List announcements for a classroom using query parameters. Only accessible to teachers.
    """
    return crud_announcement.get_multi(db, classroom_id, skip=skip, limit=limit)

@router.get("/{classroom_id}", response_model=List[Announcement])
def read_announcements(
    classroom_id: UUID,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    List announcements for a classroom. Only accessible to teachers.
    """
    return crud_announcement.get_multi(db, classroom_id, skip=skip, limit=limit)

@router.get("/announcement/{announcement_id}", response_model=Announcement)
def read_announcement(
    announcement_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get a single announcement by ID. Only accessible to teachers.
    """
    return crud_announcement.get(db, announcement_id)

@router.put("/{announcement_id}", response_model=Announcement)
def update_announcement(
    announcement_id: UUID,
    announcement_in: AnnouncementUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Update an existing announcement. Only accessible to teachers.
    """
    db_announcement = crud_announcement.get(db, announcement_id)
    return crud_announcement.update(db, db_announcement, announcement_in)

@router.delete("/{announcement_id}", response_model=Announcement)
def delete_announcement(
    announcement_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Delete an announcement by ID. Only accessible to teachers.
    """
    return crud_announcement.remove(db, announcement_id)

@router.get("/student/{classroom_id}")
def read_announcements_for_student(
    classroom_id: UUID,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    List announcements and tasks for a classroom for students.
    """
    return crud_announcement.get_multi(db, classroom_id, skip=skip, limit=limit)