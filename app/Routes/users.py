import os
from dotenv import load_dotenv
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from Cruds.users import create_user, get_all_students, get_all_teachers, get_all_sponsors, get_all_institutes, get_user_by_id, signin, get_all_users,get_all_users, update_profile_picture, upload_profile_picture
from Schemas.users import Signin, UserCreate, UserOut
from config.deps import get_current_user
from config.session import get_db
from config.permission import require_type
from Schemas.Token import Token
from config.security import oauth2_scheme

from datetime import datetime, timedelta
load_dotenv()
router = APIRouter()

@router.post("/signin", response_model=dict)
def sign_in(user : Signin, db: Session = Depends(get_db)):
    try:
        print(f"Signin attempt for email: {user.email}")
        result = signin(db, user.email, user.password)
        print(f"Signin successful for email: {user.email}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in signin endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/signup", response_model=UserOut)
def sign_up(user: UserCreate, db: Session = Depends(get_db)) -> UserOut:
    if user.user_type == "admin":
        raise HTTPException(status_code=400, detail="User type 'admin' is not allowed for user creation. Please use 'student', 'teacher', 'sponsor', or 'institute'.")
    return create_user(db, user)

@router.get("/me", response_model = UserOut)
def Me(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> UserOut:
    """
    Get the current user's details.
    
    Args:
        db (Session): Database session dependency.
        token (str): OAuth2 token dependency.

    Returns:
        UserOut: The current user's details.
    """
    user = get_current_user(db = db, token = token)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user


@router.post("/", response_model=UserOut)
def create_user_endpoint(user: UserCreate, db: Session = Depends(get_db), _ = Depends(require_type("admin"))) -> UserOut:
    """
    Create a new user.
    
    Args:
        user (UserCreate): User creation schema.
        db (Session): Database session dependency.

    Returns:
        UserOut: The created user details.
    """
    return create_user(db, user)

@router.get("/", response_model=List[UserOut])
def get_users(db : Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))) -> List[UserOut]:
    return get_all_users(db)

@router.get("/{user_id}", response_model=UserOut)
def get_user(user_id: str, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> UserOut:
    """
    Get a user by ID.
    
    Args:
        user_id (str): User ID.
        db (Session): Database session dependency.
        token (str): OAuth2 token dependency.

    Returns:
        UserOut: The user details.
    """
    return get_user_by_id(db, user_id)

@router.post("/profile_picture", response_model=UserOut)
def upload_profile_picture_endpoint(user_id: str, profile_pic_url: str, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> UserOut:
    """
    Upload a profile picture for the user.
    
    Args:
        user_id (str): User ID.
        profile_pic_url (str): URL of the profile picture.
        db (Session): Database session dependency.
        token (str): OAuth2 token dependency.

    Returns:
        UserOut: The updated user details.
    """
    return upload_profile_picture(db, user_id, profile_pic_url)

@router.put("/profile_picture", response_model=UserOut)
def update_profile_picture_endpoint(user_id: str, profile_pic_url: str, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> UserOut:
    """
    Update the profile picture for the user.
    
    Args:
        user_id (str): User ID.
        profile_pic_url (str): URL of the new profile picture.
        db (Session): Database session dependency.
        token (str): OAuth2 token dependency.

    Returns:
        UserOut: The updated user details.
    """
    return update_profile_picture(db, user_id, profile_pic_url)

"""
@router.get("/generate_sas_url/{blob_name}", response_model=str)
def generate_sas_url(
    blob_name: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> str:
    account_name = os.getenv("AZURE_STORAGE_ACCOUNT_NAME")
    container_name = os.getenv("AZURE_STORAGE_CONTAINER_NAME")
    account_key = os.getenv("AZURE_STORAGE_ACCOUNT_KEY")
    if not all([account_name, container_name, account_key]):
        raise HTTPException(status_code=500, detail="Azure Storage environment variables are not set.")
    sas_token = generate_blob_sas(
        account_name=str(account_name),
        container_name=str(container_name),
        blob_name=blob_name,
        account_key=str(account_key),
        permission=BlobSasPermissions(write=True, create=True),  # ✅ REQUIRED for new blobs
        expiry=datetime.utcnow() + timedelta(minutes=15)
    )
    blob_url = f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
    return blob_url


def generate_read_url(blob_name: str) -> str:
    account_name = os.getenv("AZURE_STORAGE_ACCOUNT_NAME")
    container_name = os.getenv("AZURE_STORAGE_CONTAINER_NAME")
    account_key = os.getenv("AZURE_STORAGE_ACCOUNT_KEY")
    if not all([account_name, container_name, account_key]):
        raise Exception("Azure Storage environment variables are not set.")
    sas_token = generate_blob_sas(
        account_name=str(account_name),
        container_name=str(container_name),
        blob_name=blob_name,
        account_key=str(account_key),
        permission=BlobSasPermissions(read=True),
        expiry=datetime.utcnow() + timedelta(hours=1)
    )
    return f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
"""
@router.get("/students/all", response_model=List[UserOut])
def get_students(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> List[UserOut]:
    return get_all_students(db)

@router.get("/teachers/all", response_model=List[UserOut])
def get_teachers(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> List[UserOut]:
    return get_all_teachers(db)

@router.get("/sponsors/all", response_model=List[UserOut])
def get_sponsors(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> List[UserOut]:
    return get_all_sponsors(db)

@router.get("/institutes/all", response_model=List[UserOut])
def get_institutes(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> List[UserOut]:
    return get_all_institutes(db)