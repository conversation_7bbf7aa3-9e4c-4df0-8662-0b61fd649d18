from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    SECRET_KEY: str = "a3f9f8b8d7e8f6c9a7b5c6d8f9e8b7a6"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24
    FERNET_KEY: str = "your-32-byte-fernet-key-here-for-testing"

    # Azure Storage Configuration (optional for testing)
    AZURE_STORAGE_CONNECTION_STRING: Optional[str] = None
    AZURE_STORAGE_ACCOUNT_NAME: Optional[str] = None
    AZURE_STORAGE_CONTAINER_NAME: Optional[str] = None
    AZURE_STORAGE_ACCOUNT_KEY: Optional[str] = None

    # MongoDB Configuration for Notifications and Logs
    MONGODB_HOST: str = "***********"
    MONGODB_PORT: int = 27017
    MONGODB_USERNAME: str = "admin"
    MONGODB_PASSWORD: str = "secret123"
    MONGODB_AUTH_DATABASE: str = "admin"
    MONGODB_DATABASE: str = "edufair_logs"

    GEMINI_API_KEY: str

    class Config:
        env_file = ".env"

settings = Settings()
