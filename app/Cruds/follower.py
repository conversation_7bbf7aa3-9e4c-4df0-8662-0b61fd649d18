import uuid
from sqlalchemy.orm import Session
from fastapi import HTTPException
from Models.users import User
from Models.follower import Follower, Follow_Request
from Schemas.users import UserOut
from Schemas.follower import (
    FollowerCreateSchema, FollowerOutputSchema, FollowRequestCreateSchema,
    FollowRequestOutputSchema, FollowerRemoveSchema, FollowRequestRemoveSchema, FollowerSchema,
    FollowRequestSchema
)
from Schemas.Token import Token

def create_follow_request(
        db: Session, user_id: uuid.UUID, follower_id: uuid.UUID
) -> FollowRequestOutputSchema:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    
    follower = db.query(User).filter(User.id == follower_id).first()
    if not follower:
        raise HTTPException(status_code=404, detail="Follower not found.")
    
    if user_id == follower_id:
        raise HTTPException(status_code=400, detail="User cannot follow themselves.")
    
    already_following = db.query(Follower).filter(
        Follower.user_id == user_id, Follower.follower_id == follower_id
    )
    if already_following.first():
        raise HTTPException(status_code=400, detail="User is already following this follower.")
    already_requested = db.query(Follow_Request).filter(
        Follow_Request.user_id == user_id, Follow_Request.follower_id == follower_id
    )
    if already_requested.first():
        raise HTTPException(status_code=400, detail="Follow request already exists.")
    follow_request = Follow_Request(user_id=user_id, follower_id=follower_id)
    db.add(follow_request)
    db.commit()
    db.refresh(follow_request)
    
    return FollowRequestOutputSchema.from_orm(follow_request)

def accept_follow_request(
        db: Session, user_id: uuid.UUID, follower_id: uuid.UUID
) -> FollowerOutputSchema:
    follow_request = db.query(Follow_Request).filter(
        Follow_Request.user_id == user_id, Follow_Request.follower_id == follower_id
    ).first()
    
    if not follow_request:
        raise HTTPException(status_code=404, detail="Follow request not found.")

    follower = Follower(user_id=user_id, follower_id=follower_id)
    db.add(follower)
    db.delete(follow_request)
    db.commit()
    
    return FollowerOutputSchema.from_orm(follower)
def remove_follow_request(
        db: Session, user_id: uuid.UUID, follower_id: uuid.UUID
) -> FollowRequestOutputSchema:
    follow_request = db.query(Follow_Request).filter(
        Follow_Request.user_id == user_id, Follow_Request.follower_id == follower_id
    ).first()
    
    if not follow_request:
        raise HTTPException(status_code=404, detail="Follow request not found.")
    
    db.delete(follow_request)
    db.commit()
    
    return FollowRequestOutputSchema.from_orm(follow_request)
def remove_follower(
        db: Session, user_id: uuid.UUID, follower_id: uuid.UUID
) -> FollowerOutputSchema:
    follower = db.query(Follower).filter(
        Follower.user_id == user_id, Follower.follower_id == follower_id
    ).first()
    
    if not follower:
        raise HTTPException(status_code=404, detail="Follower not found.")
    
    db.delete(follower)
    db.commit()
    
    return FollowerOutputSchema.from_orm(follower)

def get_followers(
        db: Session, user_id: uuid.UUID
) -> list[FollowerOutputSchema]:
    followers = db.query(Follower).filter(Follower.user_id == user_id).all()
    return [FollowerOutputSchema.from_orm(follower) for follower in followers] if followers else []
def get_following(
        db: Session, user_id: uuid.UUID
) -> list[FollowerOutputSchema]:
    following = db.query(Follower).filter(Follower.follower_id == user_id).all()
    return [FollowerOutputSchema.from_orm(follow) for follow in following] if following else []
def get_follow_requests(
        db: Session, user_id: uuid.UUID
) -> list[FollowRequestOutputSchema]:
    follow_requests = db.query(Follow_Request).filter(Follow_Request.user_id == user_id).all()
    return [FollowRequestOutputSchema.from_orm(request) for request in follow_requests] if follow_requests else []
def get_followers_by_user_id(
        db: Session, user_id: uuid.UUID
) -> list[FollowerSchema]:
    followers = db.query(Follower).filter(Follower.user_id == user_id).all()
    return [FollowerSchema.from_orm(follower) for follower in followers] if followers else []
def get_follow_requests_by_user_id(
        db: Session, user_id: uuid.UUID
) -> list[FollowRequestSchema]:
    follow_requests = db.query(Follow_Request).filter(Follow_Request.user_id == user_id).all()
    return [FollowRequestSchema.from_orm(request) for request in follow_requests] if follow_requests else []
