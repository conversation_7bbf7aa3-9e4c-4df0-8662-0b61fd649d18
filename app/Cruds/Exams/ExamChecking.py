"""
CRUD operations for Exam Checking (to be implemented).
"""

from sqlalchemy.orm import Session
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from Models.ExamChecking import <PERSON><PERSON><PERSON><PERSON><PERSON>
from Schemas.Exams.ExamChecking import Ex<PERSON><PERSON><PERSON>cking<PERSON>reate, ExamCheckingUpdate
import uuid

def create_exam_checking(db: Session, checking_in: ExamCheckingCreate) -> ExamChecking:
    checking = ExamChecking(**checking_in.dict())
    db.add(checking)
    db.commit()
    db.refresh(checking)
    return checking

def get_exam_checking_by_id(db: Session, checking_id: uuid.UUID) -> ExamChecking:
    checking = db.query(ExamChecking).filter(ExamChecking.id == checking_id).first()
    if not checking:
        raise HTTPException(status_code=404, detail="ExamChecking not found.")
    return checking

def get_all_exam_checkings(db: Session):
    return db.query(ExamChecking).all()

def update_exam_checking(db: Session, checking_id: uuid.UUID, checking_update: ExamCheckingUpdate) -> ExamChecking:
    checking = get_exam_checking_by_id(db, checking_id)
    for field, value in checking_update.dict(exclude_unset=True).items():
        setattr(checking, field, value)
    db.commit()
    db.refresh(checking)
    return checking

def delete_exam_checking(db: Session, checking_id: uuid.UUID):
    checking = get_exam_checking_by_id(db, checking_id)
    db.delete(checking)
    db.commit()
    return None 