import uuid
from sqlalchemy.orm import Session
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from Models.Questions import Question, McqsQuestionsOptions
from Models.TeacherQuestion import TeacherQuestion
from Models.users import TeacherProfile
from Schemas.Exams.Questions import QuestionCreate, QuestionUpdate, McqOptionCreate, TeacherProfileOut
from config.deps import get_current_user
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
from config.mongodb import get_mongodb
import httpx
from config.config import settings
import os
from typing import Optional
import json
import re

def create_question(db: Session, question_in: QuestionCreate, user) -> Question:
    new_question = Question(
        text=question_in.text,
        answer=question_in.answer,
        Type=question_in.Type.value if hasattr(question_in.Type, 'value') else question_in.Type,
        Level=question_in.Level.value if hasattr(question_in.Level, 'value') else question_in.Level,
        imageUrl=question_in.imageUrl,
        class_id=question_in.class_id,
        subject_id=question_in.subject_id,
        chapter_id=question_in.chapter_id,
        topic_id=question_in.topic_id,
        subtopic_id=question_in.subtopic_id,
        teacher_id=user.id,
        marks=question_in.marks if hasattr(question_in, 'marks') else 1
    )
    db.add(new_question)
    db.flush()
    # Add MCQ options if provided
    if (question_in.Type.value if hasattr(question_in.Type, 'value') else question_in.Type) == 'MCQS' and question_in.options:
        for opt in question_in.options:
            option = McqsQuestionsOptions(
                question_id=new_question.id,
                option_text=opt.option_text,
                is_correct=opt.is_correct
            )
            db.add(option)
    # Add TeacherQuestion relation if user is a teacher
    if user.user_type.value == 'teacher' and user.teacher_profile:
        tq = TeacherQuestion(teacher_profile_id=user.teacher_profile.id, question_id=new_question.id)
        db.add(tq)
    db.commit()
    db.refresh(new_question)
    return new_question

def get_all_questions(db: Session):
    return db.query(Question).all()

def get_question_by_id(db: Session, question_id: uuid.UUID):
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="Question not found.")
    # Attach teacher_profile_id and teacher_profile if exists
    tq = db.query(TeacherQuestion).filter(TeacherQuestion.question_id == question_id).first()
    if tq:
        question.teacher_profile_id = tq.teacher_profile_id
        question.teacher_profile = tq.teacher_profile
    return question

def update_question(db: Session, question_id: uuid.UUID, question_update: QuestionUpdate):
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="Question not found.")
    if question_update.text is not None:
        question.text = question_update.text
    if question_update.answer is not None:
        question.answer = question_update.answer
    if question_update.Type is not None:
        question.Type = question_update.Type.value if hasattr(question_update.Type, 'value') else question_update.Type
    if question_update.Level is not None:
        question.Level = question_update.Level.value if hasattr(question_update.Level, 'value') else question_update.Level
    if question_update.imageUrl is not None:
        question.imageUrl = question_update.imageUrl
    if question_update.class_id is not None:
        question.class_id = question_update.class_id
    if question_update.subject_id is not None:
        question.subject_id = question_update.subject_id
    if question_update.chapter_id is not None:
        question.chapter_id = question_update.chapter_id
    if question_update.topic_id is not None:
        question.topic_id = question_update.topic_id
    if question_update.subtopic_id is not None:
        question.subtopic_id = question_update.subtopic_id
    if question_update.marks is not None:
        question.marks = question_update.marks
    # Update MCQ options if provided
    if (question_update.Type.value if hasattr(question_update.Type, 'value') else question_update.Type) == 'MCQS' and question_update.options is not None:
        # Remove old options
        db.query(McqsQuestionsOptions).filter(McqsQuestionsOptions.question_id == question_id).delete()
        # Add new options
        for opt in question_update.options:
            option = McqsQuestionsOptions(
                question_id=question_id,
                option_text=opt.option_text,
                is_correct=opt.is_correct
            )
            db.add(option)
    db.commit()
    db.refresh(question)
    return question

def delete_question(db: Session, question_id: uuid.UUID):
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="Question not found.")
    # Delete related TeacherQuestion entries
    db.query(TeacherQuestion).filter(TeacherQuestion.question_id == question_id).delete()
    db.delete(question)
    db.commit()
    return None

def update_mcq_option_text(db: Session, option_id: uuid.UUID, option_update) -> McqsQuestionsOptions:
    option = db.query(McqsQuestionsOptions).filter(McqsQuestionsOptions.id == option_id).first()
    if not option:
        raise HTTPException(status_code=404, detail="Option not found.")
    option.option_text = option_update.option_text
    db.commit()
    db.refresh(option)
    return option

async def get_data_for_AI(class_: str, subject: str, chapter: str):
    db = await get_mongodb()  # Get the MongoDB client from the app config
    collection = db["Edufair_AI_Data"]
    query = {
        "class": class_,
        "subject": subject,
        "chapter": chapter
    }
    result = await collection.find_one(query)
    
    if result and "_id" in result:
        result["_id"] = str(result["_id"])
    return result

def extract_questions_from_gemini_response(gemini_result):
    """
    Extracts the questions array from the Gemini LLM response.
    Handles code block markers and parses the JSON.
    """
    try:
        # Step 1: Navigate to the text field
        text = (
            gemini_result["candidates"][0]["content"]["parts"][0]["text"]
        )
        # Step 2: Remove code block markers (```json ... ```)
        match = re.search(r"```json\s*(.*?)\s*```", text, re.DOTALL)
        if match:
            questions_json = match.group(1)
        else:
            # Fallback: try to remove any backticks if present
            questions_json = text.strip("`")
        # Step 3: Parse the JSON
        questions = json.loads(questions_json)
        return questions
    except Exception as e:
        raise ValueError(f"Failed to extract questions: {e}")

async def call_gemini_with_ai_data(
    class_: str,
    subject: str,
    chapter: str,
    no_of_questions: int = 3,
    topic: Optional[str] = None,
    subtopic: Optional[str] = None
):
    # Cap the number of questions at 6
    no_of_questions = min(max(no_of_questions, 1), 6)

    # Step 1: Get data from MongoDB
    ai_data = await get_data_for_AI(class_, subject, chapter)
    if ai_data is None:
        raise HTTPException(status_code=404, detail="Subject- Class - Chapter Data not found")
    text_data = ai_data["data"] if ai_data and "data" in ai_data else ""

    # Step 2: Prepare Gemini API call
    api_key = getattr(settings, "GEMINI_API_KEY", None) or os.getenv("GEMINI_API_KEY")
    if not api_key:
        raise RuntimeError("GEMINI_API_KEY not set in environment or config.")

    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key={api_key}"

    # Build the advanced prompt
    prompt_parts = [
        f"You are an expert question generator for grade {class_} {subject}.",
        f"Given the following chapter: '{chapter}'.",
        f"Here is the reference text data (do NOT copy questions from it, only use it for inspiration):\n{text_data}",
        f"Generate {no_of_questions} unique, original, and challenging questions in JSON format.",
        "Each question must match EXACTLY the following JSON structure, using only these fields:",
        '''
[
  {
    "text": "What is the atomic number of Hydrogen?",
    "answer": "1",
    "Type": "MCQS",
    "Level": "EASY",
    "imageUrl": null,
    "marks": 1,
    "options": [
      {"option_text": "1", "is_correct": true},
      {"option_text": "2", "is_correct": false},
      {"option_text": "8", "is_correct": false},
      {"option_text": "0", "is_correct": false}
    ]
  },
  {
    "text": "Explain the process of nuclear fission.",
    "answer": "Nuclear fission is the process in which a heavy nucleus splits into two lighter nuclei, releasing energy.",
    "Type": "LONG",
    "Level": "MEDIUM",
    "imageUrl": null,
    "marks": 5
  }
]
        ''',
        "For MCQS, always include the 'options' array as shown. For text-based questions (SHORT or LONG), do not include 'options'.",
        "The 'Level' field must be one of ONLY these three values: EASY, MEDIUM, or HARD.",
        "Output ONLY a JSON array of objects, each with the above fields. Do not include any text outside the JSON.",
        "Questions must be new and not copied from the provided data. Ensure variety and depth."
    ]
    if topic:
        prompt_parts.append(f"Focus on the topic: '{topic}'.")
    if subtopic:
        prompt_parts.append(f"If possible, relate to the subtopic: '{subtopic}'.")
    prompt = " ".join(prompt_parts)

    payload = {
        "contents": [
            {
                "parts": [
                    {
                        "text": prompt
                    }
                ]
            }
        ]
    }

    # Step 3: Make async HTTP request
    async with httpx.AsyncClient(timeout=60.0) as client:
        response = await client.post(url, json=payload, headers={"Content-Type": "application/json"})
        response.raise_for_status()
        gemini_result = response.json()

    # Step 4: Store prompt and answer in Training_data collection
    from config.mongodb import get_mongodb
    db = await get_mongodb()
    training_collection = db["Training_data"]
    parsed_questions = extract_questions_from_gemini_response(gemini_result)
    await training_collection.insert_one({
        "prompt": prompt,
        "llm_answer": gemini_result,
        "parsed_questions": parsed_questions,
        "meta": {
            "class": class_,
            "subject": subject,
            "chapter": chapter,
            "topic": topic,
            "subtopic": subtopic,
            "no_of_questions": no_of_questions
        }
    })

    return {
        "questions": parsed_questions
    } 


