# WebSocket API Documentation

## Overview

The EduFair exam system uses WebSocket connections for real-time exam session monitoring, answer synchronization, and anti-cheating measures. WebSocket endpoints provide persistent bidirectional communication between students and the exam system.

## Base URL

```
ws://localhost:8000/api/ws/exam-session/{session_id}
```

## Authentication

All WebSocket connections require authentication via JWT token passed as a query parameter:

```
ws://localhost:8000/api/ws/exam-session/{session_id}?token={jwt_token}
```

## WebSocket Endpoints

### 1. Exam Session WebSocket

**Endpoint:** `/api/ws/exam-session/{session_id}`

**Description:** Real-time exam session monitoring and answer synchronization

**Connection Requirements:**
- Valid JWT token (student authentication)
- Active exam session
- Student must own the session

#### Connection Flow

1. **Initial Connection**
   - WebSocket connection established
   - Server validates session ownership and status
   - Server sends initial session state

2. **Session State Message (Server → Client)**
   ```json
   {
     "type": "session_resume",
     "current_answers": {
       "question_id_1": "student_answer_1",
       "question_id_2": "student_answer_2"
     },
     "remaining_time_seconds": 1800,
     "strikes": 0,
     "session_id": "session_123"
   }
   ```

#### Client Messages (Client → Server)

##### 1. Heartbeat Message
**Purpose:** Keep connection alive and prevent timeout
```json
{
  "type": "heartbeat"
}
```

**Response:** No response, but server updates last heartbeat timestamp

##### 2. Answer Synchronization
**Purpose:** Save student answers in real-time
```json
{
  "answers": {
    "question_id_1": "updated_answer_1",
    "question_id_2": "updated_answer_2"
  }
}
```

**Response:**
```json
{
  "type": "answers_saved",
  "timestamp": "2024-01-15T10:30:45.123456"
}
```

##### 3. Cheating Detection
**Purpose:** Report potential cheating behavior
```json
{
  "type": "cheat"
}
```

**Response:** 
- If strikes < 3: No response, strikes incremented
- If strikes >= 3: Disqualification message
```json
{
  "event": "disqualified",
  "reason": "Cheating detected."
}
```

#### Server Behavior

##### Connection Validation
- Validates JWT token and student identity
- Checks session ownership (student_id must match)
- Verifies session status is "active"
- Rejects connection if any validation fails

##### Timeout Handling
- **Heartbeat Timeout:** 40 seconds (2 missed heartbeats)
- **Auto-disconnect:** After 2 missed heartbeats
- **Status Update:** Sets session status to "disconnected"

##### Anti-Cheating Measures
- **Strike System:** Tracks suspicious behavior
- **Auto-disqualification:** After 3 strikes
- **Real-time Monitoring:** Continuous session state tracking

##### Answer Persistence
- **Real-time Sync:** Answers saved immediately
- **Redis Storage:** Temporary storage during session
- **PostgreSQL Backup:** Final storage on session completion

## Message Types Reference

### Client → Server Messages

| Type | Description | Payload |
|------|-------------|---------|
| `heartbeat` | Keep connection alive | `{}` |
| `answers` | Sync student answers | `{"answers": {"qid": "answer"}}` |
| `cheat` | Report cheating behavior | `{}` |

### Server → Client Messages

| Type | Description | Payload |
|------|-------------|---------|
| `session_resume` | Initial session state | Session data with answers and time |
| `answers_saved` | Confirmation of saved answers | Timestamp |
| `disqualified` | Cheating disqualification | Reason message |

## Error Handling

### Connection Errors

| Error Code | Description | Action |
|------------|-------------|--------|
| `WS_1008_POLICY_VIOLATION` | Invalid session or ownership | Connection rejected |
| `WS_1011_INTERNAL_ERROR` | Timeout or system error | Connection closed |

### Session States

| State | Description |
|-------|-------------|
| `active` | Normal exam session |
| `disconnected` | Connection lost |
| `disqualified` | Cheating detected |
| `terminated_by_admin` | Admin intervention |
| `ended` | Session completed |

## Security Considerations

### Authentication
- JWT token validation on connection
- Student identity verification
- Session ownership validation

### Anti-Cheating
- Real-time behavior monitoring
- Strike-based disqualification system
- Connection timeout detection
- Answer integrity verification

### Data Protection
- Secure WebSocket connections (wss:// in production)
- Encrypted answer transmission
- Session isolation per student

## Usage Examples

### JavaScript Client Example

```javascript
const token = "your_jwt_token";
const sessionId = "session_123";
const ws = new WebSocket(`ws://localhost:8000/api/ws/exam-session/${sessionId}?token=${token}`);

ws.onopen = function() {
    console.log('Connected to exam session');
    
    // Send heartbeat every 30 seconds
    setInterval(() => {
        ws.send(JSON.stringify({type: 'heartbeat'}));
    }, 30000);
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'session_resume':
            console.log('Session resumed:', data);
            break;
        case 'answers_saved':
            console.log('Answers saved:', data.timestamp);
            break;
        case 'disqualified':
            console.log('Disqualified:', data.reason);
            ws.close();
            break;
    }
};

// Save answers
function saveAnswers(answers) {
    ws.send(JSON.stringify({answers: answers}));
}

// Report cheating (if needed)
function reportCheating() {
    ws.send(JSON.stringify({type: 'cheat'}));
}
```

### Python Client Example

```python
import asyncio
import websockets
import json

async def exam_session_client():
    token = "your_jwt_token"
    session_id = "session_123"
    uri = f"ws://localhost:8000/api/ws/exam-session/{session_id}?token={token}"
    
    async with websockets.connect(uri) as websocket:
        # Send heartbeat every 30 seconds
        async def heartbeat():
            while True:
                await websocket.send(json.dumps({"type": "heartbeat"}))
                await asyncio.sleep(30)
        
        # Start heartbeat task
        heartbeat_task = asyncio.create_task(heartbeat())
        
        try:
            async for message in websocket:
                data = json.loads(message)
                print(f"Received: {data}")
                
                if data.get("type") == "session_resume":
                    print("Session resumed successfully")
                elif data.get("type") == "answers_saved":
                    print("Answers saved successfully")
                elif data.get("event") == "disqualified":
                    print(f"Disqualified: {data.get('reason')}")
                    break
                    
        finally:
            heartbeat_task.cancel()
```

## Monitoring and Administration

### Admin Endpoints

The system provides HTTP endpoints for admin monitoring:

- `GET /api/admin/exam-session/{session_id}` - View session details
- `POST /api/admin/exam-session/{session_id}/submit` - Force submit session
- `POST /api/admin/exam-session/{session_id}/terminate` - Terminate session
- `GET /api/admin/exam-sessions/active` - List all active sessions

### Redis Storage

Session data is stored in Redis with key pattern: `exam_session:{session_id}`

**Fields:**
- `student_id` - Student identifier
- `exam_id` - Exam identifier
- `answers` - JSON string of student answers
- `strikes` - Number of cheating strikes
- `status` - Session status
- `start_time` - Session start timestamp
- `duration` - Exam duration in seconds
- `last_heartbeat` - Last heartbeat timestamp

## Best Practices

1. **Connection Management**
   - Implement automatic reconnection logic
   - Handle connection failures gracefully
   - Monitor connection health

2. **Answer Synchronization**
   - Save answers frequently (every 30-60 seconds)
   - Implement local answer caching
   - Handle network interruptions

3. **Security**
   - Validate all incoming messages
   - Implement proper error handling
   - Monitor for suspicious behavior

4. **Performance**
   - Use efficient JSON serialization
   - Implement message compression if needed
   - Monitor WebSocket connection limits

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check JWT token validity
   - Verify session ownership
   - Ensure session is active

2. **Timeout Disconnections**
   - Implement proper heartbeat mechanism
   - Check network connectivity
   - Monitor server resources

3. **Answer Loss**
   - Implement local answer caching
   - Use periodic answer synchronization
   - Handle reconnection scenarios

### Debug Information

Enable debug logging to monitor WebSocket connections:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Version History

- **v1.0** - Initial WebSocket implementation
- **v1.1** - Added anti-cheating measures
- **v1.2** - Enhanced error handling and monitoring 